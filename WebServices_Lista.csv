"Nome do Arquivo","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","WebService","M�todos"
"TCMSS001.prw","adm_vendas\Comissões\Webservices\TCMSS001.prw",":		Fonte contendo os webservices da nova RV.","GetGrupos","Put - Inser��o de cadastro; Get - Consulta parametriza��es RV.; Post - Aprova��o de Regra RV; Get - Consulta de areas elegiveis a receber RV; Get - Consulta de cargos por area dos elegiveis a receber RV.; Get - Consulta de cargos por area dos elegiveis a receber RV.; Put - Inser��o dos dados analiticos da RV; Get - Consulta Extrato Analitico RV.; Post - Aprova��o Analitica do RV; Post - Motor de calculo; Get - Consulta Extrato Sintetico RV.; Get - Consulta de executivos elegiveis da RV.; Get - Consulta de referencias; Get - Consulta Agrupadores RV.; Get - Consulta Indicadores RV.; Get - Consulta de GSN da RV.; Get - Geracao do relatorio excel.; Get - Consulta parametriza��es RV.; Get - Consulta os acessos dos usuarios RV; Get - Consulta usuarios RV; Put - Inclus�o de usuarios RV; Get - Consulta rotinas do RV; Get - Consulta acessos RV; Put - Inclus�o de acessos para usuarios RV; Get - Consulta rotinas do RV"
"TCMSS002.prw","adm_vendas\Comissões\Webservices\TCMSS002.prw","WSService Apurados_Webservice  ""Webservice para retornar o % de apurados""","Apurados_Webservice","Le_Apurados - Metodo de retorno de % de apurados"
"WSMXMECM.prw","adm_vendas\Comissões\Webservices\WSMXMECM.prw","WebService para integra��o do Maxime com o ECM","",""
"WSMXMECMClient.prw","adm_vendas\Comissões\Webservices\WSMXMECMClient.prw","WebService - Descri��o n�o encontrada","",""
"TGCVS001.prw","adm_vendas\Contratos\Corporativo\WebService\TGCVS001.prw","���           � WebService do portal de contrato corporativo da Totvs.    ���","CONTRATOCORPORATIVO","GetContrato - Metodo que verifica se o cliente logado possui contrato de licencas corporativas.; GetPasso - Metodo que verifica qual passo o cliente parou em seu ultimo acesso ao processo de reajuste corporativo digital.; PutPasso - Metodo que atualiza qual passo o cliente parou em seu ultimo acesso ao processo de reajuste corporativo digital.; GetCNPJ - Metodo que busca a lista de todos os cnpj usado pelo cliente logado em seu contrato de licencas corporativas.; ValidaCNPJ - Metodo que valida se CNPJ informado e o principal no contrato de licencas corporativas.; GetMetrica - Metodo que busca o tipo de metrica definida no contrato do cliente.; GetReajuste - Metodo que grava valores das metricas informadas pelo site e retorna valores reajustados.; GetReajDIPJ - Metodo que grava valores das metricas Dipjs informadas pelo site e retorna se grava��o foi realizada.; GetMemoria - Metodo que retorna a memoria de calculo sobre o reajuste dos anos anteriores.; SimGetMemoria - Metodo que retorna a memoria de calculo sobre o reajuste dos anos anteriores para o simulador.; GetPagamento - Metodo que busca as formas de pagamento para os valores de reajustes calculados.; PutPagamento - Metodo que grava a forma de pagamento selecionada atrav�s do site.; GetTributacao - Metodo para buscar a lista com os tipos de tributa��o."
"TGCVS002.prw","adm_vendas\Contratos\Corporativo\WebService\TGCVS002.prw","���    � WebServices do projeto DIPJ                                 ���","WS_DIPJ","BuscaExcecao - Busca dados na tabela de Exce��es Corporativas (ZAW); BuscaMemCalc - Busca mem�ria de c�lculo na tabela de Previs�o Faturamento (ZB2); HabWS - Habilita ou n�o o acesso aos webservices"
"TGCVS003.prw","adm_vendas\Contratos\Corporativo\WebService\TGCVS003.prw","���    � WebServices REST para Projeto Corporativo                   ���","AtuLinkPHI","POST - Atualiza Log de Envio de Workflow"
"TGCIIWS1.PRW","adm_vendas\Contratos\Novo Intera\TGCIIWS1.PRW","WsRestFul InteraIlimitadoCNPJPendentes  ""Intera Ilimitado - Retorna os CNPJs Pendentes de Comprova��o de ROL""","InteraIlimitadoContratoSocial","Get - Intera Ilimitado - Retorna os CNPJs Pendentes de Comprova��o de ROL; Get - Intera Ilimitado - Retorna os CNPJs vinculado ao CNPJ Pai; Post - Intera Ilimitado - Incluir ou excluir um CNPJ filho vinculado ao CNPJ principal do cliente; Post - Intera Ilimitado - Envio do arquivo de comprova��o ECD; Get - Intera Ilimitado - Metricas; Post - Intera Ilimitado - Envio do c�digo do TICKET gerado no Zendesk; Post - Intera Ilimitado - Envio do formulario de comprova��o; Post - Intera Ilimitado - envio que o cliente est� de acordo com o valor comprovado; Get - Intera Ilimitado - Retorna os CNPJs Pendentes de Comprova��o de ROL; Post - Intera Ilimitado - links de download do arquivo Contrato Social do Cliente"
"TGCIIWS3.PRW","adm_vendas\Contratos\Novo Intera\TGCIIWS3.PRW","WsRestFul InteraEnvioECD  ""Metodo para enviar o arquivo de comprovacao do ECD""","InteraAtuStatus","Post - Metodo para enviar o arquivo de comprovacao do ECD; Get - Metodo para enviar a metrica apos a confirmacao dos valores de comprovacao.; Post - Metodo para enviar o codigo do TICKET gerado no Zendesk, referente a comprovacao.; Post - Metodo para enviar o formulario e comprovacao.; Post - Metodo para enviar que o cliente está  de acordo com o valor comprovado.; Get - Metodo para retornar a memoria de Calculo.; Post - Altera o status do registro caso a metrica seja confirmada."
"TGCIIWS4.PRW","adm_vendas\Contratos\Novo Intera\TGCIIWS4.PRW","Webservice REST que retorna informa��es referente ao(s) CNPJ","TGetClienteIntera","Get - Valida o Cliente informado na URL"
"TGCIIWS5.PRW","adm_vendas\Contratos\Novo Intera\TGCIIWS5.PRW","Webservice REST que grava informa��es referente ao(s) CNPJ","TPostCliIntera","Post - Processo para atualizar os CNPJ envolvendo o Novo Intera"
"TGCIIWS6.PRW","adm_vendas\Contratos\Novo Intera\TGCIIWS6.PRW","Webservice REST que retorna informa��es referente ao(s) CNPJ","TGetAtualizaCNPJ","Get - Valida o Cliente informado na URL"
"TGCIIWS7.PRW","adm_vendas\Contratos\Novo Intera\TGCIIWS7.PRW","Webservice REST que grava informa��es referente ao(s) CNPJ","TPostIncExcCNPJ","Post - Processo para incluir/excluir os CNPJ envolvendo o Novo Intera"
"TGCIIWS8.PRW","adm_vendas\Contratos\Novo Intera\TGCIIWS8.PRW","WsRestFul InteraIlimitadoAuditaLS  ""Intera Ilimitado - Audita LS""","InteraIlimitadoAuditaLS","Post - Intera Ilimitado - Audita LS Inclus�o CNPJ agregado"
"TGCTS001.PRW","adm_vendas\Contratos\Webservice\TGCTS001.PRW","WsRestFul TGCTBaseCompleta   ""Servi�o que permite consultar Contratos da Base Completa""","TGCTBaseCompleta","Get - Valida o Contrato informado na URL"
"TGCTS002.PRW","adm_vendas\Contratos\Webservice\TGCTS002.PRW","WsRestFul TGCTBaseConsolidada  ""Servi�o que permite consultar Contratos Base Consolidada""","TGCTBaseConsolidada","Get - Valida o Contrato informado na URL"
"TGCTS003.PRW","adm_vendas\Contratos\Webservice\TGCTS003.PRW","API para consulta de Contratos baseado no cadastro de Ofertas","Oferta","GET - API para consulta de Contratos baseado no cadastro de Ofertas"
"TGCTS004.PRW","adm_vendas\Contratos\Webservice\TGCTS004.PRW","Upgrade de Oferta - Contrato","contrato_upgrade_oferta","Post - WS para upgrade de oferta - contrato"
"TGCTS005.PRW","adm_vendas\Contratos\Webservice\TGCTS005.PRW","WsRestFul consulta_upgrade_oferta  ""Metodo de consulta do Status da Oferta - Upgrade de Oferta""","consulta_upgrade_oferta","Get - Consulta do Status da Oferta no Protheus"
"TGCTS006.PRW","adm_vendas\Contratos\Webservice\TGCTS006.PRW","WsRestFul ContratosCancel  ""Post via API - Contratos Cancelamento""","ContratosCancel","Post - Chave dos Contratos"
"ServicosVendasWs.prw","adm_vendas\CRM\CRM Servicos\WebService\ServicosVendasWs.prw","WSRESTFUL ServicosVendas  'API de integra��o das entidades de vendas de servicos no CRM: oportunidades, propostas, time de vendas'","ServicosVendas",""
"SimuladorWS.prw","adm_vendas\CRM\CRM Servicos\WebService\SimuladorWS.prw","WSRESTFUL simuladorOrcamento  ""Or�amento de Servi�os portalCRM""","simuladorOrcamento","GET - Valida se a margem envia WF; GET - Valida se a unidade de servi�o est� no PSA; GET - Verifica se o usu�rio tem permiss�o para acrescimo; POST - Verifica se usu�rio permite incluir time de vendas no simulador; GET - Retorna estrutura GAR do Vendedor"
"TCRMX181.PRW","adm_vendas\CRM\Diversos\TCRMX181.PRW","WebService - Descri��o n�o encontrada","",""
"WS_METRICAS.prw","adm_vendas\CRM\Royalties\WS_METRICAS.prw","���WebService� WS_METRICAS �Autor  � Eder Oliveira   � Data � 20/03/2014  ���","WS_METRICAS","BuscaDatas - Busca Datas sem m�tricas da unidade; GravaMetricas - Grava metricas das unidades; LeCalculo - Le os dados do c�lculo mensal; VerFranquia - Verifica se a franquia n�o � propria; DiaLimInfMet - Verifica o dia limite para o informe de metrica"
"F0100101.prw","adm_vendas\CRM\WebServices\F0100101.prw","WSRESTFUL WSRGT12CLI  ""Cadastro de Clientes/Prospect TOTVS12""","WSRGT12CLI","GET - Retorna dados de cliente/prospect do TOTVS12"
"F0100201.prw","adm_vendas\CRM\WebServices\F0100201.prw","WSRESTFUL WSRGT12ESN  ""Estrutura de Negocios TOTVS12""","WSRGT12ESN","GET - Retorna dados da estrutura de negocios"
"F0100501.prw","adm_vendas\CRM\WebServices\F0100501.prw","WSRESTFUL WSRGT12PROPOSAL  ""Propostas de Vendas Aptas""","WSRGT12PROPOSAL","GET - Retorna dados das propostas de vendas aptas do TOTVS12"
"F0102201.prw","adm_vendas\CRM\WebServices\F0102201.prw","WSRESTFUL WSRPT12RETORC  ""Web Service - Or�amento Otimizado""","WSRPT12RETORC","PUT - Retorna estrutura json informando se obteve sucesso na inclusao do cliente"
"TCRMS001.PRW","adm_vendas\CRM\WebServices\TCRMS001.PRW","���     �WS para retorno de indicadores do CRM ao sistema visual cue ���","TCRMS001","INCCLIXCOP - nclui amarra��o cliente x copiloto"
"TCRMS002.PRW","adm_vendas\CRM\WebServices\TCRMS002.PRW","WebService - Descri��o n�o encontrada","",""
"TCRMS003.PRW","adm_vendas\CRM\WebServices\TCRMS003.PRW","WSRESTFUL tcrms003  ""Integra��es Regra de comercializa��o CRM""","regraComercializacao","GET - Retorna regras de comercializa��o existentes no CRM; POST - Inclui regra de comercializa��o no CRM vinculando todos os cadastros necess�rios; PUT - Atualiza regra de comercializa��o no CRM incluindo novos produtos"
"TCRMS004.prw","adm_vendas\CRM\WebServices\TCRMS004.prw","WSDL Location    https://www.experianmarketing.com.br/webservice/InfoBuscaWS.asmx?WSDL","",""
"TCRMS005.prw","adm_vendas\CRM\WebServices\TCRMS005.prw","WebServices - Aprova��o de Propostas","TCRMS005","atualizafilaapr - Atualiza fila de aprova��o; retornaProposta - Retorna Dados da Proposta"
"TCRMS006.prw","adm_vendas\CRM\WebServices\TCRMS006.prw","WebServices - Inclus�o de Atividades CRM","TCRMS006","criaAtividade - Cria atividade no CRM"
"TCRMS007.prw","adm_vendas\CRM\WebServices\TCRMS007.prw","WebService para retorno de dados de vendedor e metas de sua unidade","TCRMS007","RETDADOSVEND - Este m�todo retorna os estrutura de vendas do vendedor se houverem metas cadastradas; RETESTRUTURAVEND - Este m�todo retorna os estrutura de vendas do vendedor e metas cadastradas"
"TCRMS008.PRW","adm_vendas\CRM\WebServices\TCRMS008.PRW","���     � WebService de Listagem de clientes                          ��","WSDCLIENTE","WSD_LstCli - Metodo que retorna a Lista de Clientes."
"TCRMS010.prw","adm_vendas\CRM\WebServices\TCRMS010.prw","���     �WS para retorno de indicadores do CRM ao sistema visual cue ���","TCRMS010","CRMINDICAVISUALCUE - WS RETORNO DE INDICADORES PROTHEUS vs VISUAL CUE"
"TCRMS012.prw","adm_vendas\CRM\WebServices\TCRMS012.prw","WSRESTFUL Ativa_EmpFil_TSS  ""Ativa Empresa/Filial do Cliente""","Ativa_EmpFil_TSS","POST - Ativa Empresa/Filial do Cliente"
"TCRMS013.prw","adm_vendas\CRM\WebServices\TCRMS013.prw","WSRESTFUL Lista_EmpFil_TSS  ""Lista as Empresas/Filiais do Cliente contratante do TSS 3.0""","Lista_EmpFil_TSS","GET - Lista as Empresas/Filiais do Cliente contratante do TSS 3.0"
"TCRMS014.prw","adm_vendas\CRM\WebServices\TCRMS014.prw","WSRESTFUL Reajustes_Contratuais  ""Retorna Informa��es de Reajuste dos Contratos Cloud""","Reajustes_Contratuais","GET - Retorna Informa��es de Reajuste dos Contratos Cloud"
"TCRMS015.prw","adm_vendas\CRM\WebServices\TCRMS015.prw","WSRESTFUL Busca_Consumo_Adicional_TSS  ""Busca Consumo Adicional do TSS 3.0""","Busca_Consumo_Adicional_TSS","GET - Busca Consumo Adicional do TSS 3.0"
"TCRMS016.prw","adm_vendas\CRM\WebServices\TCRMS016.prw","WSRESTFUL Altera_Consumo_Adicional_TSS  ""Altera o Consumo Adicional do TSS 3.0""","Altera_Consumo_Adicional_TSS","POST - Altera o Consumo Adicional do TSS 3.0"
"TCRMS017.prw","adm_vendas\CRM\WebServices\TCRMS017.prw","WsRestful tabelaprecos  ""WS Gest�o Tabela de pre�o Protheus CRM"" FORMAT 'application/json'","processoscrm","GET - Retorna dados de partnumber x tabela de pre�o; GET - Retorna dados de agrupadores x partnumber x tabela de pre�o; PUT - Altera tabela de pre�o; GET - Retorna dados que ser�o alterados no PUT ; PUT - Altera Lot de pre�os por indice fixado na integra��o; GET - Retorna dados que ser�o alterados no PUT ; PUT - Desativa produtos para novas vendas CRM; GET - Retorna dados de Acesso por Unidade (AZA); GET - Retorna dados de Entidade (AZ2); GET - Retorna dados de CNAE; GET - Retorna lista de tabela de pre�o x produto; PUT - Altera informa��es base portf�lio; GET - Retorna dados do processo de Job"
"TCRMS018.prw","adm_vendas\CRM\WebServices\TCRMS018.prw","WebService de Retorno de dados de GO","TCRMS018","WsRetEmailGARGO - Retorna e-mail GAR / GO do cliente"
"TCRMS019.PRW","adm_vendas\CRM\WebServices\TCRMS019.PRW","WebService para tratamento de solicita��es de antecipa��o de suporte MPN N1 (via FLUIG)","",""
"TCRMS020.prw","adm_vendas\CRM\WebServices\TCRMS020.prw","WSRESTFUL Busca_Consumo_Filial_TSS  ""Busca Consumo do Cliente por Filial""","Busca_Consumo_Filial_TSS","GET - Busca Consumo do Cliente por Filial"
"TCRMS021.prw","adm_vendas\CRM\WebServices\TCRMS021.prw","WSRESTFUL Busca_Consumo_Periodo_TSS  ""Busca Consumo do Cliente por Periodo""","Busca_Consumo_Periodo_TSS","GET - Busca Consumo do Cliente por Periodo"
"TCRMS022.prw","adm_vendas\CRM\WebServices\TCRMS022.prw","WSRESTFUL TCRMS022A  ""Retorna Papeis do Usu�rio""","TCRMS022D","GET - Busca papeis do usuario pelo email; GET - Busca oportunidade do ESN; GET - Retorna contatos da conta; GET - Retorna Arquitetos e Demonstradores"
"TCRMS023.PRW","adm_vendas\CRM\WebServices\TCRMS023.PRW","SERVICOS CLOUD","WSSERVICOSCLOUD",""
"TCRMS024.prw","adm_vendas\CRM\WebServices\TCRMS024.prw","WebService para tratar certifica��o de membros MPN entre Fluig x Protheus","",""
"TCRMS025.prw","adm_vendas\CRM\WebServices\TCRMS025.prw","WSRESTFUL TCRMS025A  ""Retorno das Milhas ""","TCRMS025C","GET - Extra��o das Milhas dos Participantes ; GET - Extra��o das Milhas dos Participantes ; POST - D�bito de Milhas"
"TCRMS026.PRW","adm_vendas\CRM\WebServices\TCRMS026.PRW","WSRESTFUL generatePOR  'API de disparo de integra��o cadastrada na POR interempresas'","generatePOR","POST - Inclus�o de "
"TCRMS028.PRW","adm_vendas\CRM\WebServices\TCRMS028.PRW","WSRESTFUL TCRMS028  ""Retorna valor da TIU   ""","TCRMS028","GET - Retorna valor da TIU para a data informada , caso n�o informado retorna o valor corrente  "
"TCRMS029.prw","adm_vendas\CRM\WebServices\TCRMS029.prw","WSRESTFUL IncAlt_Cliente_Cloud  ""Inclus�o/Altera��o de Clientes Cloud""","IncAlt_Cliente_Cloud","POST - Inclus�o/Altera��o de Clientes Cloud"
"TCRMS031.prw","adm_vendas\CRM\WebServices\TCRMS031.prw","API para bloquear a transmissao de notas do cliente.","BRD_EMPFIL_TSS","POST - Bloqueia/Reativa/Desativa Empresa/Filial do TSS 3.0"
"TCRMS032.prw","adm_vendas\CRM\WebServices\TCRMS032.prw","WSRESTFUL VagasDisponiveis  STR0001	//""Retona quantidade de vagas dispon�veis de um munic�pio""","DadosUnidade","GET - Retorna dados da unidade"
"TCRMS034.prw","adm_vendas\CRM\WebServices\TCRMS034.prw","WSRESTFUL StoreConsultas  ""Retona dados gerado por consultas cadastradas para Middle TOTVS Store""","TS034Assinatura","GET - Dados gerado por consultas cadastradas para Middle TOTVS Store; POST - Inclui no CRM uma proposta TOTVS Store; GET - Devolve o blob de um arquivo de Proposta para download; POST - Recebe doc assinado para uplpoad da proposta"
"TCRMS036.prw","adm_vendas\CRM\WebServices\TCRMS036.prw","WSRESTFUL tcrms036  ""Integra��o - Simulador Intera Ilimitado""","tcrms036","GET - PING PARA MANTER PROCESSO ATIVO NO PROTHEUS; POST - RETORNO DE PRODUTOS"
"TCRMS037.prw","adm_vendas\CRM\WebServices\TCRMS037.prw","WSRESTFUL tcrms037  ""CONSULTA PRODUTOS - PAGINA WEB INTERA ILIMITADO""","RegraDependencia","GET - RETORNA PRODUTOS"
"TCRMS038.prw","adm_vendas\CRM\WebServices\TCRMS038.prw","manuten��o de Propostas no CRM","crmInfoJobsPedidosStore","Post - Inclui registro em fila para Integra��es; GET - Lista os cart�es de credito utilizados pelo cliente; PUT - Altera dados do cart�o de cr�dito; GET - Lista os cart�es de credito e itens de boleto utilizados pelo cliente; PUT - Altera os cart�es de credito e itens de boleto utilizados pelo cliente; GET - Retornar informa��es das execu��es dos jobs integra��o pedido Store"
"TCRMS040.prw","adm_vendas\CRM\WebServices\TCRMS040.prw","WSRESTFUL TCRMS040  ""EXPORTA ESTRUTURA E M�TRICAS - NOVO INTERA""","TCRMS040","GET - EXPORTA ESTRUTURA E M�TRICAS"
"TCRMS042.prw","adm_vendas\CRM\WebServices\TCRMS042.prw","WSRESTFUL TCRMS042  ""REPRECIFICA��O NOVO INTERA""","TCRMS042","GET - REPRECIFICA��O NOVO INTERA"
"TCRMS044.PRW","adm_vendas\CRM\WebServices\TCRMS044.PRW","Ws para retornar informa��es de um cadastro","",""
"TCRMS045.PRW","adm_vendas\CRM\WebServices\TCRMS045.PRW","WSRESTFUL ModelStruct  EncodeUTF8( STR0001 )	 FORMAT ""application/json""		//""Realiza sincronimos de dados conforme a estrutura recebida""","",""
"TCRMS046.PRW","adm_vendas\CRM\WebServices\TCRMS046.PRW","Ws para retornar informa��es para or�amento de Servi�os","",""
"TCRMS047.PRW","adm_vendas\CRM\WebServices\TCRMS047.PRW","WSRESTFUL TCRMS047  ""Edi��o de propostas HTML""","modeloImpressao","GET - Retorna HTML para edi��o; POST - Grava HTML Editado; GET - Retorna PDF; GET - Retorna componente HTML; POST - Grava componente HTML; PUT - Atualiza componente HTML; DELETE - Exclui componente HTML; GET - Retorna modelo HTML; POST - Grava modelo HTML; PUT - Atualiza modelo HTML; DELETE - Exclui modelo HTML"
"TCRMS048.prw","adm_vendas\CRM\WebServices\TCRMS048.prw","WebServices - Aprova��o de Propostas - Novo modelo de tela","TCRMS048","AtuFilaAprovadores - Atualiza fila de aprova��o; BuscaDadosProposta - Busca Dados da Proposta"
"TCRMS050.PRW","adm_vendas\CRM\WebServices\TCRMS050.PRW","Ws para os Componentes do Simulador de Or�amento de Servi�os","",""
"TCRMS051.PRW","adm_vendas\CRM\WebServices\TCRMS051.PRW","WSRESTFUL logJobsTi  ""Retorna informa��es de execu��o de jobs - TI""","logJobsTi","GET - Retorna informa��es de execu��o de jobs - TI"
"TCRMS052.prw","adm_vendas\CRM\WebServices\TCRMS052.prw","WSRESTFUL aprovTransf  ""Aprova��o de Transfer�ncia de Contas""","aprovTransf","POST - Aprova��o de Transfer�ncia de Contas"
"TCRMS053.PRW","adm_vendas\CRM\WebServices\TCRMS053.PRW","Ws para os Componentes do Simulador de Or�amento de Servi�os","",""
"TCRMS054.prw","adm_vendas\CRM\WebServices\TCRMS054.prw","Ws para os Componentes do Simulador de Or�amento de Servi�os","",""
"TCRMS055.prw","adm_vendas\CRM\WebServices\TCRMS055.prw","Ws para os Componentes da Matriz de Canais, Risco e Complexidade","",""
"TCRMS056.PRW","adm_vendas\CRM\WebServices\TCRMS056.PRW","WSRESTFUL Contas  'API de integra��o de Contas: Clientes, Prospects e Suspects'","Contas",""
"TCRMS057.PRW","adm_vendas\CRM\WebServices\TCRMS057.PRW","WSRESTFUL Monitor_Campo_QTDLicenca  ""Monitoramento do preenchimento do campo B1_XQTDLI (Quantidade Licen�as)""","Monitor_Campo_QTDLicenca","GET - Monitoramento do preenchimento do campo B1_XQTDLI (Quantidade Licen�as)"
"TCRMS059.prw","adm_vendas\CRM\WebServices\TCRMS059.prw","WSRESTFUL WSSTATUSPTG  ""Retorna status da Tabela PTG - Contratos x HLCloud""","WSSTATUSPTG","GET - Retorna status da Tabela PTG - Contratos x HLCloud"
"TCRMS060.prw","adm_vendas\CRM\WebServices\TCRMS060.prw","WSRESTFUL WSZENDESK  ""Retorna dados do Zendesk""","WSZENDESK","GET - Retorna quantidade de clientes pendentes para integra��o com Zendesk"
"TCRMS061.prw","adm_vendas\CRM\WebServices\TCRMS061.prw","WSRESTFUL VendasDigitais  'API de integra��o de Vendas Digitais: Ofertas'","VendasDigitais",""
"TCRMS063.prw","adm_vendas\CRM\WebServices\TCRMS063.prw","WSRESTFUL monitorAuditTi  'Servi�o para monitoramento dos gatilhos de audit trail na TI'","monitorAuditTi","GET - Retorna informa��es de gatilhos do Audit Trail"
"TCRMS064.PRW","adm_vendas\CRM\WebServices\TCRMS064.PRW","WSRESTFUL Vendas  'Integra��o em Vendas'","Vendas",""
"TCRMS066.PRW","adm_vendas\CRM\WebServices\TCRMS066.PRW","WSRESTFUL tcrms066  ""Lista Proposta do cliente  ou prospects informado.""","tcrms066","GET - Retorna as proposta do ."
"TCRMS068.PRW","adm_vendas\CRM\WebServices\TCRMS068.PRW","WSRESTFUL CallCenter  'API integra��o do modulo CallCenter (SIGATMK)'","CallCenter",""
"TCRMS069.PRW","adm_vendas\CRM\WebServices\TCRMS069.PRW","WSRESTFUL GarantiaEstendidaTotvs  'API de integra��o de Garantia Estendida'","GarantiaEstendidaTotvs",""
"TCRMS070.prw","adm_vendas\CRM\WebServices\TCRMS070.prw","WSRESTFUL tcrms070  ""WS Simulador de Servi�os""","tcrms070","GET - Retorna estrutura inicial necessaria para simulador de servi�o ."
"TCRMS072.prw","adm_vendas\CRM\WebServices\TCRMS072.prw","oBrowse:Set(STRX006) // ""Log de Integra��o - Simulador de Servi�os 3.0""","",""
"TCRMS078.prw","adm_vendas\CRM\WebServices\TCRMS078.prw","endpoint TCRMS078 API","TCRMS078funcao",""
"TTMKS001.prw","adm_vendas\CRM\WebServices\TTMKS001.prw","{Protheus.doc} WebService","WsECMCRM","EnviaQualif - Envio da Avalia��o de Qualifica��o - Central de Oportunidades; RetQualif - Retorno da Avalia��o de Qualifica��o - Central de Oportunidades; MovOport - Analisa movimenta��es na oportunidade - Call Center; RetNotif - Retorno da Notifica��o - Call Center"
"WSClient_ECMColleagueService.prw","adm_vendas\CRM\WebServices\WSClient_ECMColleagueService.prw","WebService - Descri��o n�o encontrada","",""
"TCTTS001.PRW","adm_vendas\CTT\WebService\TCTTS001.PRW","Defini��o da estrutura do webservice ..","CTT",""
"AceleratorApply.prw","adm_vendas\EasySales\API\AceleratorApply.prw","WSRESTFUL Login  ""Valida tokenId"" FORMAT ""application/json""","GetFile","GET - Valida tokenId da aplica��o do Easy Sales; GET - Retorna arquivo SDF conforme token informado; GET - Retorna arquivo para backup conforme os aceleradores do token informado; GET - Retorna lista de aceleradores conforme o token informado; GET - Retorna lista de aceleradores conforme o token informado"
"IntegPcSis.prw","adm_vendas\Integracões\IntegPcSis.prw","WsRestFul PcSisCondPagto  ""Metodo Responsavel por Retornar as Cond. Pagto que Sofreram algum tipo de Movimentacao""","PcSisConPCcpnJ","Get - Cadastro de Cond. Pagamento; Get - Consulta Cadastro de Produtos; Put - Atualiza Cadastro de Produtos; Get - Consulta Cadastro de Vendedors; Put - Atualiza Cadastro de Vendedors; Get - Consulta Cadastro de Prospectss; Put - Atualiza Cadastro de Prospectss; Get - Consulta Cadastro de Clientess; Put - Atualiza Cadastro de Clientess; Get - Consulta Cadastro de TabPrecos; Put - Atualiza Cadastro de TabPrecos; Put - Processo Responsavel por Gerar a Oportunidade/Proposta ; Put - Atualiza Cadastro de TabPrecos; Post - Upload de Propostas; Get - Status Proposta; Put - Atualiza Cadastro de Clientes; Get - Consulta Ofertas Pc Sistemas"
"TINTWS01.PRW","adm_vendas\Integracões\TINTWS01.PRW","WsRestFul TAdqAcesso  ""Solita��o da Chave de Acesso para consumo das API - Integra��o Adquiridas""","TCORPCLIM","Get - Chave de Acesso; Get - Consulta os Dados de Clientes utilizando como Chave o CPF/CNPJ; Put - Cadastramento dos dados do cliente; Get - Consulta os Dados de Clientes utilizando como Chave o CPF/CNPJ; Put - Cadastramento dos dados do contato; Put - Processo para gerar a Oportunidade e Proposta; Post - Processo para gerar/Atualizar a Oportunidade e Proposta; Get - Consulta do Status da Proposta no Protheus; Put - Processo para deixar os itens de Software Bemacash ativos, Cancalemanto de Hardware Bemacash e Troca de Numero de Serie de Hardware Bemacash - Integra��o Adquiridas (Bematech); Put - Metodo para altera��o do status de proposta; Get - Consulta do Status da Solicitacao para cancelar uma proposta no contrato (devolucao) ; Put - Processo para atualizar o tabela de controle de registros exportados; Put - Processo para atualizar o tabela de controle de registros exportados; Get - Metodo de Consulta de Cadastro de Produtos, registros novos/atulizados - Integra��o Adquiridas; Get - Metodo de Consulta de Cadastro de Vendedores, registros novos/atulizados - Integra��o Adquiridas; Get - Metodo de Consulta de Cadastro de Tabela de Preco, registros novos/atulizados - Integra��o Adquiridas; Get - Metodo de Consulta de Cadastro de Condicao de Pagamento, registros novos/atulizados - Integra��o Adquiridas; Get - Metodo de Consulta de Cadastro de Indice de Reajuste, registros novos/atulizados - Integra��o Adquiridas; Get - Metodo de Consulta de Cadastro de Cidades, registros novos/atulizados - Integra��o Adquiridas; Get - Metodo de Consulta de Cadastro de CNAE X Segmento, registros novos/atulizados - Integra��o Adquiridas; Get - Metodo de Consulta de Cadastro de Segmento X Sub-Segmento, registros novos/atulizados - Integra��o Adquiridas; Post - Consulta se um produto pode ser realizada por uma revenda para um determinado cliente; Post - Consulta se um produto determinado produto pode ser vendido por uma revenda; Put - Servi�o respons�vel por voltar o status dos registros, para assim ser poss�vel fazer uma nova carga; Put - Servi�o respons�vel integrar os pedidos de compras.; Get - Consulta se existe o Produto Ativo no Contrato do cliente - Integra��o Adquiridas; Put - Processo para ativar as licen�as misterchef - Integra��o Adquiridas (Bematech); Get - Consulta da amarra��o de entidades cont�beis.; Get - Consulta de Cadastro de Grupos Economicos de Clientes; Post - Cadastro de Grupo Economico; Get - Metodo de Consulta de Boletos Gerados - Integra��o Adquiridas; Get - Metodo de Consulta Status de Recebimento de Titulos - Integra��o Adquiridas; Get - Consulta de Titulos a receber - Integra��o Adquiridas (CiaShop); Get - Consulta de Notas e seus itens - Integra��o Adquiridas; Post - Recep��o de notifica��es da plataforma Vindi; Put - Habilita licen�a de produtos TEF de acordo com pedido externo e CNPJ; Put - Processo de cancelamento de produtos TEF (M�ltiplos CNPJ) � TOTVS STORE; PUT - Altera o e-mail da nota fiscal do cliente MPN"
"TINTWS02.prw","adm_vendas\Integracões\TINTWS02.prw","WsRestFul TAdqPutProvComissoes  ""Inclus�o de provis�o de comiss�es do N1""","TAdqPutComissoes","Put - Inclus�o de Provis�o de Comiss�es; Put - Inclus�o de Provis�o de Comiss�es"
"TINTWS03.PRW","adm_vendas\Integracões\TINTWS03.PRW","WsRestFul TAdqConSupN1  ""Consulta de revendas Bematech p/ verificar se possui N1""","TAdqConSupN1","Post - Consulta de revendas Bematech p/ verificar se possui N1"
"TINTWS04.PRW","adm_vendas\Integracões\TINTWS04.PRW","Webservice REST que retorna dados dos contratos dos clientes PC Sistemas","ListaContratos","GET - Retorna dados dos contratos dos clientes PC Sistemas"
"TINTWS05.PRW","adm_vendas\Integracões\TINTWS05.PRW","WS para processamento do Billing das Adquiridas.","TAdqBilling","Post - WS para inclus�o do Billing Mensal"
"TINTWS06.prw","adm_vendas\Integracões\TINTWS06.prw","Ws para retornar informa??es dos contratos para o empodera","EmpoderaTae","GET - Retorna dados dos contratos alterados em uma data espec?fica; GET - Retorna dados de somente um contrato; GET - Retorna dados Carga Geral Contratos a partir de uma data; GET - Retorna dados de Contatos dos clientes ; GET - Retorna dados de Contatos dos clientes ; GET - Retorna dados de Contatos dos clientes ; POST - Retorna dados dos titulos; GET - Retorna dados de Propostas dos clientes ; GET - Retorna a rela��o da hierarquia x clientes. ; GET - Retorna a rela��o da hierarquia x clientes. ; GET - Retorna dados do cliente. ; GET - Retorna todos contatos vincculados ao cliente. ; GET - Retorna a hieraquia com os canais.; Post - Retorna uma lista de contratos; GET - Retorna a rela��o de contratos Intera. ; GET - Retorna dados do cronograma financeiro do contrato; GET - Retorna os clientes atrelados a um determinado contrato. ; GET - Retorna dados do cronograma financeiro do contrato; GET - Retorna dados de clientes.; GET - Retorna os clientes atrelados a um determinado contrato. "
"TINTWS11.prw","adm_vendas\Integracões\TINTWS11.prw","WSRESTFUL TINTWS11  ""Ws de controle de envio de status para parceiro"" FORMAT ""application/json""","TINTWS11","PUT - Ws de controle de envio de status para parceiro"
"TINTWS12.prw","adm_vendas\Integracões\TINTWS12.prw","Api para retornar resumo de contratos ao licenciadores externos","TAdqcontratoext","GET - Retorna dados dos contratos externos em uma CNPJ principal"
"TCADS001.prw","Backoffice\Cadastro\Integracoes\TCADS001.prw","WsRestful TCADS001  ""WS Alterar Cadastro de Fornecedores""","TCADS001","GET - Retorna dados do fornecerdor (SA2); GET - Retorna o c�digo do munic�pio (CC2); PUT - Altera conta banc�ria (SA2)"
"TCADS002.prw","Backoffice\Cadastro\Integracoes\TCADS002.prw","WSRESTFUL Cadastros  'API de integra��o de cadastros.'","Cadastros",""
"TCOMS001.PRW","Backoffice\Compras\WebServices\TCOMS001.PRW","WSRESTFUL tcoms001  ""Integra��o - Compras - Protheus e Fluig""","tcoms001","GET - Retorno conteudo arquivo; PUT - Grava��o Integra��o "
"TCFGA002.PRW","Backoffice\Configurador\TCFGA002.PRW","WebService - Descri��o n�o encontrada","",""
"TCFGW001.PRW","Backoffice\Configurador\TCFGW001.PRW",":		Metodo responsavel por retornar todos usuarios do protheus","GetUserRules","Get - Lista Todos Usuarios Do Protheus; Get - Lista todos os grupos do usuario; PUT - Altera o Usuario usando a API do PAdrao User; GET - Pega os dados do Usuario no Protheus; Get - Lista todos as regras dos grupos do usuario"
"TCTBWS01.PRW","Backoffice\Contabilidade\Integracoes\TCTBWS01.PRW","Webservice REST que retorna dados dos contratos dos clientes PC Sistemas","TPProvisao","GET - Retorna dados dos Tipos de Provis�o para o Provisionamento Cont�bil"
"TCTBWS02.PRW","Backoffice\Contabilidade\Integracoes\TCTBWS02.PRW","WSRESTFUL TCadFluigZZR  'API para Inclus�o das solicita��es feitas no formul�rio FLUIG'","TGetP54Status","POST - Inclus�o Formul�rio FLUIG na ZZR; POST - Retorno do fluig na P54; GET - Traz os dados das solicita��es feitas no formul�rio FLUIG tabela ZZR; GET - Traz os dados das solicita��es feitas no formul�rio FLUIG tabela P54"
"TCTBA056MI.PRW","Backoffice\Contabilidade\TCTBA056MI.PRW","WSRESTFUL GSPLEASINGMEX  'gspleasingmex'","GSPLEASINGCOL","POST - API Contabiliza��o; POST - API Contabiliza��o; POST - API Contabiliza��o"
"TCTBS001.PRW","Backoffice\Contabilidade\TCTBS001.PRW","���           � WebService do protal de autoatendimento da Totvs.         ���","FORMPCOT","CCPCOT - RELA��O DE CENTRO DE CUSTO; ITCTBPCOT - RELA��O DE ITEM CONTABIL; CLASSVL - RELA��O DE CLASSE DE VALOR; APRPCOT - RELA��O DE APROVADORES; CONTCTB - RELA��O DO PLANO DE CONTAS"
"TFATS001.prw","Backoffice\Faturamento\TFATS001.prw","Portal de auto-atendimento.","AUTOATENDIMENTO","CONS_PENDENCIAS - RETORNA RELA��O DE TITULOS PENDENTES PARA DETERMINADO CLIENTE; PROX_VENCTO - RETORNA PRIMEIRO VENCIMENTO DIA UTIL DO CLIENTE ; CONTAS_PAGAR - RETORNA RELACAO DE PAGAMENTOS A UM DETERMINADO FORNECEDOR ; CONS_CABECALHO - RETORNA CABECALHO PARA CONSULTA DE NOTA ; CONS_DETALHE - RETORNA DETALHE PARA CONSULTA DE NOTA ; STAT_VIAGEM - ALTERA STATUS PRESTACAO DE CONTAS DE VIAGENS; EMP_FILIAL - RETORNA RELACAO DE EMPRESAS FILIAIS; CONF_FORNEC - VALIDA SE FORNECEDOR EXISTE EM ALGUMA DAS EMPRESAS; CONS_CONT_COLIG - CONSULTA CONTRATO E COLIGADAS VINGULADOS A PARTIR DA PROPOSTA; CONS_CLOUD_CABEC - RETORNA CABECALHO PARA CONSULTA DE NOTA FISLCAL CLOUD; CONS_CLOUD_PENDE - RETORNA RELA��O DE TITULOS PENDENTES PARA DETERMINADO CLIENTE DE CLOUD; CONS_CLIPCSIST - RETORNA CLIENTE TOTVS E SUA RESPECTIVA LOJA A PARTIR DO DE/PARA PC SISTEMAS; CONS_TITABERTOS - TITULOS EM ABERTO NO FINANCEIRO (SER�O LISTADOS NO PORTAL FINANCEIRO PC SISTEMAS); CONS_GETXML - CAPTURA XML DE NOTAS DE ACORDO COM PARAMETRO ENVIADO; CONS_PCCABEC - PC SISTEMAS - RETORNA CABECALHO PARA CONSULTA DE NOTA "
"TFATS002.PRW","Backoffice\Faturamento\TFATS002.PRW","���           � WebService do protal de autoatendimento da Totvs.         ���","AUTATEND","FICHA_FATURAMENTO - RETORNA LINK PARA PLANILHA GERADA COM A FICHA DE FAT. DO CLIENTE; CLI_CORP - RETORNA INDICADOR SE CLIENTE � CORPORATIVO OU N�O; INSERT_CNPJ - INSERE NOVO CNPJ PARA CLIENTE CORPORATIVO; DELETE_CNPJ - INATIVA CNPJ PARA CLIENTE CORPORATIVO; CNPJ_CORP - RETORNA RELACAO DE CNPJS ATIVOS PARA O CLIENTE"
"TFATS003.PRW","Backoffice\Faturamento\TFATS003.PRW","���           � WebService do portal de autoatendimento da Totvs.         ���","AUTOATENDIMENTO_RESUMO","GUIA_ATEND - RETORNA DADOS PARA GUIA ATENDIMENTO ; RPS_ATEND - RETORNA DADOS PARA RPS ATENDIMENTO ; BOLETO_ATEND - RETORNA DADOS PARA BOLETO ATENDIMENTO ; PIX_ATEND - RETORNA DADOS DO PIX ATENDIMENTO"
"TFATS004.prw","Backoffice\Faturamento\TFATS004.prw","���WebService�TFATS004           �Autor �Paulo Sampaio   �Data �12/01/2016���","TFATS004","CCListaBandeira - Lista cadastro de Bandeira de Cart�o de Cr�dito.; CCListaCartao - Lista cart�es de credito do cliente.; CCGravaCartao - Inclui/Altera/Exclui informacoes do cartao de credito do cliente.; CCAtualizaContrato - Atualiza forma de pagamento no contrato do cliente.; CCLiberaProc - Libera processo Cart�o de Cr�dito portal do cliente Autoatendimento."
"TFATS006.PRW","Backoffice\Faturamento\TFATS006.PRW","���     � 			WebService Desenvolvido para o Projeto PVT            ���","TdiPrjPvt","PvtGetCli - Metodo Responsavel por Retornar as Informacoes de Cliente e Contrato que Estao Relacionados com um Pvt; PvtCtaRec - Metodo Responsavel por Retornar os Titulos em Aberto dos Clientes que Estao Relacionados com um Pvt; PvtBxRec - Metodo Responsavel por Retornar as Baixas dos Titulos Relacionadas aos Clientes que Estao Vinculados a um Pvt; PvtGetCom - Metodo Responsavel por Retornar as Informacoes Referentes ao Comissionamento que esta Relacionado a um Pvt; GetGEBSO - M�todo respons�vel por retornar as linhas de GE e BSO do cliente"
"TFATS008.prw","Backoffice\Faturamento\TFATS008.prw","WsRestFul PvConsStatus  ""Serviço responsável Consulta do Status de pedidos de Vendas""","PvConsStatus","Put - Serviço responsável Cancelar os pedidos de Vendas."
"TFATS009.prw","Backoffice\Faturamento\TFATS009.prw","WsRestFul PvCanVA  ""Serviço responsável Cancelar os pedidos de Vendas""","PvCanVA","Put - Serviço responsável Cancelar os pedidos de Vendas."
"TFATS010.prw","Backoffice\Faturamento\TFATS010.prw","WsRestFul PvVirtual  ""Serviço responsável integrar os pedidos de Vendas""","PvVirtual","Put - Serviço responsável integrar os pedidos de Vendas."
"TIGPW000MI.PRW","Backoffice\Financeiro\Gesplan\TIGPW000MI.PRW","WSRESTFUL GSPFINANCINGMEX  'gspfinancingmex'","GSPFINANCINGCOL","POST - Inclus�o de Movimenta��o Banc�ria; POST - Inclus�o de Movimenta��o Banc�ria; POST - Inclus�o de Movimenta��o Banc�ria"
"TIGPW001MI.PRW","Backoffice\Financeiro\Gesplan\TIGPW001MI.PRW","WSRESTFUL GSPCASHMEX  'gspcashmex'","GSPCASHCOL","POST - Inclus�o de Movimenta��o Banc�ria; POST - Inclus�o de Movimenta��o Banc�ria; POST - Inclus�o de Movimenta��o Banc�ria"
"TFINR024.PRW","Backoffice\Financeiro\TFINR024.PRW",":		Fun��o para retornar os Acumulados","",""
"TFINS001.PRW","Backoffice\Financeiro\TFINS001.PRW","���           � WebService do protal de autoatendimento da Totvs.         ���","FLUIG","BANCO - RETORNA RELA��O DE BANCOS CADASTRADOS; FORNEC - RETORNA INFORMA��ES DO FORNECEDOR; EMPRPAG - RETORNA INFORMA��ES DA EMPRESA PAGADORA; NATUR - RETORNA INFORMA��ES DA NATUREZA; CONSPAG - CONSULTA DE TITULOS A PAGAR; INCPAG - INCLUS�O DE TITULOS A PAGAR; CCUSTO - RELA��O DE CENTRO DE CUSTO; ITEMCTB - RELA��O DE ITEM CONTABIL; CLVL - RELA��O DE CLASSE DE VALOR; TPTIT - RELA��O DE TIPO DE TITULO; MOEDA - RELA��O DE MOEDAS; APROVSP - RELA��O DE APROVADORES; RESPUNID - RETORNA O EMAIL DO RESPONSAVEL PELA UNIDADE"
"TFINS002.prw","Backoffice\Financeiro\TFINS002.prw","WebService - Descri��o n�o encontrada","",""
"TFINS010.prw","Backoffice\Financeiro\TFINS010.prw","WSSERVICE WSHDPC  ""Servi�o destinado a fornecer m�todos para bloqueio/desbloqueio do Suporte (HD e Chamados) da PC Sistemas""","WSHDPC","GetStatusCliente - Retorna os status do cliente: se est� inadimplente, se pode ser atendido e se est� cancelado.; GetValorContrato - Retorna o valor da medi��o do contrato de suporte na compet�ncia atual.; SetCadCliente - Atualiza os campos Carteira, Formador de Opini�o e Estrela do cadastro do Cliente."
"TFINS013.PRW","Backoffice\Financeiro\TFINS013.PRW",".: M�todo de consulta das valida��es das provis�es de despesa","TGETVLDESPR","Get - Valida��o dos dados ref. � provis�o de despesas (m�todo GET)"
"TFINW001.PRW","Backoffice\Financeiro\TFINW001.PRW",":		Metodo responsavel por retornar os dados sinteticos de envio e retorno do serasa","GetBuscaAvanc","Get - Consulta dashboard Serasa.; Get - Consulta Cliente Serasa.; Get - Consulta Detalhes por Status.; Get - Filtro Avan�ado."
"TFINW002.PRW","Backoffice\Financeiro\TFINW002.PRW",":		M�todo de bloqueio de licen�as","AVBLOQLIC","PUT - Bloqueio de Licen�as"
"TIINTERCPTDIMEN.PRW","Backoffice\Interceptor\WebServices\Dimensa\TIINTERCPTDIMEN.PRW","Webservice de leitura e gravacao do TIINTERCPTDIMEN","TIINTERCPTDIMEN","GET - Retorna o status de uma requisi��o interceptada empresa DIMENSA; POST - Recebe uma requisi��o empresa DIMENSA"
"TIWSINTERCEPTOR.PRW","Backoffice\Interceptor\WebServices\TIWSINTERCEPTOR.PRW","Webservice de leitura e gravacao do TIINTERCEPTORS","TIINTERCEPTORWS","GET - Retorna o status de uma requisi��o interceptada; POST - Recebe uma requisi��o"
"DEVPS002.prw","DevOps\DEVPS002.prw","WebService - Descri��o n�o encontrada","",""
"DEVPS010.prw","DevOps\DEVPS010.prw","WSRESTFUL devopsAlias  ""Solicita��o e consulta de reserva de alias dentro do ambiente""","devopsAlias","GET - Retorna as alias do usu�rio logado/informado; PUT - Realiza a reserva de alias para o usu�rio logado/informado"
"DEVPW001.prw","DevOps\DEVPW001.prw","WSRESTFUL TIDEVOPS  'API Automa��o DEVOPS' FORMAT ""application/json,text/html""","TIDEVOPS",""
"TINCWS01.prw","Incorporador\TINCWS01.prw","WSRESTFUL WebHookInc  'API de integra��o do Webhook de Incorpora��es'","StatusP98","Get - Gera a consulta das tabelas a serem integradas para a vers�o web do Integramais; Get - Busca os cadastros dispon�veis para integra��o pela vers�o web do Integramais com o Protheus; Get - Gera a consulta dos status das tabelas a serem integradas com a vers�o web do Integramais"
"TINCWS02.prw","Incorporador\TINCWS02.prw","TINCWS02 API: oData by Integrador","TINCWS02",""
"TIWSIPAAS.prw","iPaaS\TIWSIPAAS.prw","WSRESTFUL TIWSIPAAS  'API para recep��o de requisi��es do iPaaS' FORMAT ""application/json,text/html""","TIWSIPAAS",""
"GWMSUSERACESSOS.PRW","Monitor Jobs\Acessos\GWMSUSERACESSOS.PRW","Api para monitorar os jobs de schedules","GWMSWSUSERACESSOS","Get - Get expenses information"
"GWMSWSACESSOS.prw","Monitor Jobs\Acessos\GWMSWSACESSOS.prw","Api para monitorar os acessos","GWMSWSACESSOS","Get - Get expenses information"
"GWMSWSUSERSSO.PRW","Monitor Jobs\Acessos\GWMSWSUSERSSO.PRW","Api para monitorar os usuarios sem SSO","GWMSWSUSERSSO","Get - Get expenses information"
"GWTOTVSBAIXAS.prw","Monitor Jobs\Baixas\GWTOTVSBAIXAS.prw","Api para monitorar a gera��o de propostas e contratos Totvs","GW_TOTVS_BAIXAS","Get - Get cnab information"
"GWTOTVSCNAB.prw","Monitor Jobs\CNAB\GWTOTVSCNAB.prw","Api para monitorar a gera��o de propostas e contratos Totvs","GWTOTVSCNAB","Get - Get cnab information"
"GWMSWSDESPA.prw","Monitor Jobs\Contas a Pagar\GWMSWSDESPA.prw","Api para monitorar pagamentos de PA","GWMSWSDESPA","Get - Get expenses information"
"GWMSWSDESPESAS.prw","Monitor Jobs\Contas a Pagar\GWMSWSDESPESAS.prw","Api para monitorar as desepesas de alugueis, condominio e iptu","GWMSWSDESPESAS","Get - Get expenses information"
"WSCTAPAGAR.prw","Monitor Jobs\Contas a Pagar\WSCTAPAGAR.prw","Api para monitorar o contas a pagar da Totvs","GW_PAGAR","Get - Get proposal information"
"GWTOTVSCS.prw","Monitor Jobs\Contratos\GWTOTVSCS.prw","Api para monitorar a gera��o de propostas e contratos Totvs","GW_TOTVSCS","Get - Get proposal information"
"WSCTRFATBRGWMS.prw","Monitor Jobs\FaturamentoBR\WSCTRFATBRGWMS.prw","WSRESTFUL WSCTRFATBRGWMS  ""Webservice to return billing information"" Format APPLICATION_JSON","WSCTRFATBRGWMS","GET - Get billing information"
"WSCTRPEDNF.prw","Monitor Jobs\FaturamentoBR\WSCTRPEDNF.prw","WSRESTFUL WSCTRPEDNF  ""Webservice to return billing information"" Format APPLICATION_JSON","WSCTRPEDNF","GET - Get billing information"
"WSFATBRGWMS.prw","Monitor Jobs\FaturamentoBR\WSFATBRGWMS.prw","WSRESTFUL WSFATBRGWMS  ""Webservice to return billing information"" Format APPLICATION_JSON","WSFATBRGWMS","GET - Get billing information"
"WSFATMIGWMS.prw","Monitor Jobs\FaturamentoBR\WSFATMIGWMS.prw","WSRESTFUL WSFATMIGWMS  ""Webservice to return MI Fat"" Format APPLICATION_JSON","WSFATMIGWMS","GET - Get MI Fat"
"WSPEDFATBRGWMS.prw","Monitor Jobs\FaturamentoBR\WSPEDFATBRGWMS.prw","WSRESTFUL WSPEDFATBRGWMS  ""Webservice to return billing information"" Format APPLICATION_JSON","WSPEDFATBRGWMS","GET - Get billing information"
"GWMSWSGESPLAN.prw","Monitor Jobs\Gesplan\GWMSWSGESPLAN.prw","Api para monitorar a integracao entre Protheus e Gesplan","GWMSWSGESPLAN","Get - Get expenses information"
"GWMSWSSCHEDULES.prw","Monitor Jobs\GWMSWSSCHEDULES.prw","Api para monitorar os jobs de schedules","GWMSWSSCHEDULES","Get - Get expenses information"
"GWMSWSRECEIV.prw","Monitor Jobs\RECEIV\GWMSWSRECEIV.prw","Api para monitorar os jobs de schedules","GWMSWSRECEIV","Get - Get expenses information"
"WSRECEIVNOVOSTIT.prw","Monitor Jobs\RECEIV\WSRECEIVNOVOSTIT.prw","Webservice de resgate de informa��es de novos titulos RECEIV","WSRECEIVNOVOSTIT","GET - Get proposal information"
"WSRECEIVTITENV.prw","Monitor Jobs\RECEIV\WSRECEIVTITENV.prw","Webservice de resgate de informa��es de novos titulos RECEIV","WSRECEIVTITENV","GET - Get proposal information"
"WSRECEIVTITPARADOS.prw","Monitor Jobs\RECEIV\WSRECEIVTITPARADOS.prw","Webservice de resgate de informa��es de novos titulos RECEIV","WSRECEIVTITPARADOS","GET - Get proposal information"
"WSRECEIVTITRET.prw","Monitor Jobs\RECEIV\WSRECEIVTITRET.prw","Webservice de resgate de informa��es de novos titulos RECEIV","WSRECEIVTITRET","GET - Get proposal information"
"WSRESERVEGWMS.prw","Monitor Jobs\Reserve\WSRESERVEGWMS.prw","WSRESTFUL WSRESERVEGWMS  ""Webservice to return proposal information"" Format APPLICATION_JSON","WSRESERVEGWMS","GET - Get proposal information"
"WSVTEX.prw","Monitor Jobs\VTEX\WSVTEX.prw","WSRESTFUL WSVTEX  ""Webservice to return proposal information"" Format APPLICATION_JSON","WSVTEX","GET - Get proposal information"
"WSVTEXBILL.prw","Monitor Jobs\VTEX\WSVTEXBILL.prw","Webservice de resgate de informa��es de propostas","WSVTEXBILL","GET - Get proposal information"
"WSVTEXCONTRATOS.prw","Monitor Jobs\VTEX\WSVTEXCONTRATOS.prw","Webservice de resgate de informa��es de propostas","WSVTEXCONTRATOS","GET - Get proposal information"
"WSVTEXCRONOGRAMA.prw","Monitor Jobs\VTEX\WSVTEXCRONOGRAMA.prw","Webservice de resgate de informações de propostas","WSVTEXCRONOGRAMA","GET - Get proposal information"
"WSVTEXNOTAS.prw","Monitor Jobs\VTEX\WSVTEXNOTAS.prw","Webservice de resgate de informa��es de propostas","WSVTEXNOTAS","GET - Get proposal information"
"WSVTEXPEDIDOS.prw","Monitor Jobs\VTEX\WSVTEXPEDIDOS.prw","Webservice de resgate de informa��es de propostas","WSVTEXPEDIDOS","GET - Get proposal information"
"WSVTEXPEDXNF.prw","Monitor Jobs\VTEX\WSVTEXPEDXNF.prw","Webservice de resgate de informa��es de propostas","WSVTEXPEDXNF","GET - Get proposal information"
"WSVTEXPREBILLING.prw","Monitor Jobs\VTEX\WSVTEXPREBILLING.prw","Webservice de resgate de informações de propostas","WSVTEXPREBILLING","GET - Get proposal information"
"WSVTEXTITULOSEMITIDOS.prw","Monitor Jobs\VTEX\WSVTEXTITULOSEMITIDOS.prw","Webservice de resgate de informa��es de propostas","WSVTEXTITULOSEMITIDOS","GET - Get proposal information"
"WSAUDIT.prw","Monitor Jobs\WSAUDIT.prw","Api para monitorar o audit da Totvs","GW_AUDIT","Get - Get proposal information"
"WSPROPOSTASDASH.prw","Monitor Jobs\WSPROPOSTASDASH.prw","WSRESTFUL WSPROPOSTASDASH  ""Webservice to return proposal information"" Format APPLICATION_JSON","WSPROPOSTASDASH","GET - Get proposal information"
"WsCrmSpClient.prw","Rotinas de Ajuste\WsCrmSpClient.prw","Webservice cliente que consome o WS de integra��o com o CRM SP na matriz.","",""
"chkprodhl_client.prw","Santo Graal\graal\chkprodhl_client.prw","WebService - Descri��o n�o encontrada","",""
"WsPF04_Client.prw","Santo Graal\graal\WsPF04_Client.prw","WebService - Descri��o n�o encontrada","",""
"FWWSCSERIE1_LIC.PRW","Santo Graal\HLFisico\VpeNewInterface\FWWSCSERIE1_LIC.PRW","WebService - Descri��o n�o encontrada","",""
"FWWSCSERIE1.PRW","Santo Graal\HLFisico\VpeNewInterface\FWWSCSERIE1.PRW","WebService - Descri��o n�o encontrada","",""
"FWWSLSAUDIT.PRW","Santo Graal\HLFisico\VpeNewInterface\FWWSLSAUDIT.PRW","""<b>S�rvi�o de auditoria do License Server""  ;","","ReceiveAudit - Metodo de recebimento do xml de auditoria de hardlock; ReceiveLog - Metodo de recebimento do xml de uso dos sistemas TOTVS"
"FWWSLSKEY.PRW","Santo Graal\HLFisico\VpeNewInterface\FWWSLSKEY.PRW","""<b>S�rvi�o de disponibiliza��o da Emerg�ncialKey e da Startkey.</b><br><br>Este servi�o somente pode ser executado nas instala��es da TOTVS""  ;","","GetStartKey - Fornece a Startkey de inicializa�a� dos produtos da s�rie 1. Informe 1 no ProductId para First ou 2 para Gestor. O CNPJ somente � obrigat�rio para o First.; RemoveStartKey - Cancela a Startkey de inicializa�o do Produto da s�rie 1; GetEmergencialKey - Senha de emerg�ncia; GetLSEmergencialKey - Senha de emerg�ncia - Precisa do ambiente Maxime montado; GetLicenseKey - Fornece a licen�a do License Server; GetLicenseKeyPath - Fornece a licen�a do License Server; GetLicenseKeyContent - Fornece a licen�a do License Server; GetLicenseInternalKey - Fornece a licen�a do License Server para consultores TOTVS; CanUseCustomerAuto - Informa se o cliente pode usar o auto atendimento; GetNewEmergencialKey - Nova Senha de emerg�ncia; GetCnpjsBySigaMat - Retorna uma sequ�ncia de Cnpjs, baseado no SIGAMAT enviado. Os CNPJ ser�o devolvidos separados por |; CanUseEmergKey - Informa se o cliente pode usar senha de emerg�ncia; GetListHL - Retorna a lista de HL; GetHLListOK - Retorna a lista de HL que n�o possui bloqueo"
"HLFWSCL01.prw","Santo Graal\HLFisico\Webservice\HLFWSCL01.prw","WsData 	AS String","",""
"HLFWSCL02.prw","Santo Graal\HLFisico\Webservice\HLFWSCL02.prw","WebService - Descri��o n�o encontrada","",""
"HLFWSCL03.prw","Santo Graal\HLFisico\Webservice\HLFWSCL03.prw","WSSERVICE WBSInfoTOTVSAuthorizationCode  ""WebService para grava��o do AuthorizationCode"";","WBSInfoTOTVSAuthorizationCode","SendSolicitation - Envia para a TOTVS o arquivo XML com a solicita��o da Autoriza��o"
"HLFWSCL04.PRW","Santo Graal\HLFisico\Webservice\HLFWSCL04.PRW","WebService para descri��o","","GetEmergLic - Retorna a senha de emergencia do Portal"
"THLTS001.prw","Santo Graal\WebService\THLTS001.prw","WSRESTFUL WSRESUMT12CLI  ""Resumo do Cliente TOTVS12""","WSRESUMT12CLI","GET - Retorna dados do contrato do cliente do TOTVS12"
"THLTS002.prw","Santo Graal\WebService\THLTS002.prw","WSRESTFUL WSREGBLQ12CLI  ""Levantamento dos titulos x Regra de bloqueio TOTVS12""","WSREGBLQ12CLI","GET - Retorna Levantamento dos titulos x Regra de bloqueio do TOTVS12"
"THLTS003.prw","Santo Graal\WebService\THLTS003.prw","WSRESTFUL WSATVINCDESV  ""Atualiza Informa��es de V�nculo / Desvinculo""","WSATVINCDESV","POST - Atualiza Informa��es de V�nculo / Desvinculo"
"TSGES001.PRW","Servicos\Educacao Empresarial\WebService\TSGES001.PRW","WSRESTFUL TSGES001  ""Cursos de treinamentos""","TSGES001","GET - Lista de Cursos de treinamentos."
"TSGES002.PRW","Servicos\Educacao Empresarial\WebService\TSGES002.PRW","WSRESTFUL TSGES002  ""Clientes""","TSGES002","Get - Lista de Clientes."
"TSGES003.PRW","Servicos\Educacao Empresarial\WebService\TSGES003.PRW","WSRESTFUL TSGES003  ""Executivos de Vendas""","TSGES003","Get - Lista de Executivos de Vendas"
"TSGES004.PRW","Servicos\Educacao Empresarial\WebService\TSGES004.PRW","WSRESTFUL TSGES004  ""Agrupadores""","TSGES004","Get - Lista de Agrupadores"
"CREOSWS.PRW","Servicos\Engenharia Servicos\CREOSWS.PRW","Estruturas do WebService","CREOSWS","GetAuthPkg - Autentica o pacote acelerador; PutMovtPkg - Gera movimentacao do pacote acelerador; GetInfoPkgPages - Busca paginas do pacote acelerador; GetInfoPkgData - Busca dados do pacote acelerador; GetHistPkgPages - Busca paginas movimentacao do acelerador; GetHistPkgData - Busca dados movimentacao do acelerador; GetHistoryUser - Busca movimenta�ao do usuario"
"GPSDRE.prw","Servicos\Engenharia Servicos\GPSDRE.prw","GPS-WebService para o DRE","GPSDRE","ListaDRE - Retorna o DRE conforme parametros"
"GPSINTFRQ_CLIENT.prw","Servicos\Engenharia Servicos\GPSINTFRQ_CLIENT.prw","WebService - Descri��o n�o encontrada","",""
"GPSINTFRQ.prw","Servicos\Engenharia Servicos\GPSINTFRQ.prw","GPS-WebService para integracao de franquias","GPSINTFRQ","BuscaDados - Retorna os dados para a franquia; GravaDados - Grava os dados no ambiente compartilhado; getTabelas - Mostra tabelas cadastradas para download"
"LIBERALIC.prw","Servicos\Engenharia Servicos\LIBERALIC.prw","WSSERVICE LIBERALIC  STR0001 // ### ""Libera��o de Licen�as p/ Consultores TOTVS""","","LibCons_HIS - Hist�rico das Libera��es"
"SOURCESTAMP.PRW","Servicos\Engenharia Servicos\SOURCESTAMP.PRW","Estruturas do WebService","SOURCESTAMP","GetAuthUsr - Autentica o usuario do acelerador/TDS; GetInfoRisk - Busca informa�oes de risco para o usu�rio"
"WsFranquia.prw","Servicos\Engenharia Servicos\WsFranquia.prw","WSRESTFUL BuscaDados  ""Busca dados tabela Protheus.""","getTabFrq","GET - Listagem dados de tabelas; GET - Retorna o maior recno da tabela do filtro; GET - Retorna o total de registro do filtro; GET - Retorna as tabelas disponiveis para download franquia."
"psa_client_prod.prw","Servicos\PSA\psa_client_prod.prw","WebService - Descri��o n�o encontrada","",""
"PSARECURSO.PRW","Servicos\PSA\PSARECURSO.PRW","WebService - Descri��o n�o encontrada","",""
"PSAUSUARIO.PRW","Servicos\PSA\PSAUSUARIO.PRW","WebService - Descri��o n�o encontrada","",""
"PSAWS.PRW","Servicos\PSA\PSAWS.PRW","WSRESTFUL PropostaStatus  ""Altera status da proposta no Protheus aprovado/reprovado"" FORMAT ""application/json""","RetMoedaPSA","PUT - Altera status da proposta no Protheus aprovado/reprovado; GET - Lista dos logs integra��o PSA; GET - Detalhe do registro de log integra��o PSA; DELETE - Exclui registro de log integra��o PSA; PUT - Marca status no registro de log integra��o PSA; GET - Executa metodos integracao PSA - por codigo; GET - Cotacao pendente integracao PSA; GET - Consulta de Empresas Filiais.; POST - Cadastrar Empresas Filiais.; GET - Consulta de Modalidade de Venda.; POST - Consulta de Modalidade de Venda.; GET - Consulta de Modelo Tributa��o.; POST - Cadastrar de Modelo Tributa��o.; GET - Consulta de Unidades de Servi�o.; POST - Cria��o de uma Unidade de Servi�o.; GET - Novo modelo de tributa��o considerando tabela de impostos; GET - Apontamentos pendente liberado para medi��o integracao PSA; GET - Valida centro de custo/item contabil/classe valor - PSA; GET - Retorna o custo do contrato de terceiros - PSA; GET - Retorna o custo do contrato de terceiros - PSA; POST - Gera um pedido de venda com os dados do PSA; POST - Inclui NCCs no Protheus; PUT - Exclui NCCs no Protheus; GET - Atualiza o valor recebido de um titulo; PUT - Fecha a unidade no Protheus; DELETE - Exclui o fechamento da unidade no Protheus; GET - Gera medicao de terceiros.; POST - Envia Proposta PSA; GET - Integra Relat�rios de Despesas de Atendimento aprovados; GET - Novo modelo de tributa��o considerando tabela de impostos; GET - Retorna uma lista de anexos vinculado a proposta; GET - Retorna o anexo vinculado a proposta; PUT - Atualiza o status PSX por Recno; GET - Lista os projetos com OSs da tabela PSX; GET - Aliquota para contabilizacao dos impostos do Buffer/Backlog; POST - Integracao Contatos Portal x PSA; PUT - Integracao Contatos Portal x PSA; GET - Integracao Contatos Portal x PSA; DELETE - Integracao Contatos Portal x PSA; GET - Job de usuario Protheus x Portal Office 365/ systemuser / bookableresources; GET - Atualiza permissao de Contrato para o usuario de integracao - PSA; GET - Cria token de acesso ao PSA tabela SX5; GET - Reprocessa erros da integra��o Protheus x PSA; GET - Retorna os dados da proposta; DELETE - Exclui o pedido no Protheus - PSA; POST - Gera Workflow de Aprova��o no Fluig, projetos n�o faturados PSA; GET - Atualiza o projeto nao faturado no PSA; POST - analytics503 PSA; GET - Exporta solicitacoes de viagens e prestacao de contas do Protheus para o PSA; GET - Chama funcao de validacao da data do fonte no repositorio; POST - Gera Proposta de Servicos para integrar com PSA; PUT - Grava o escopo fechado do PSA no Protheus ; GET - Consulta saldo da NF no Protheus - PSA; POST - Atualiza os par�metros para integra��o com PSA; POST - Cria parametro de integra��o PSA; PUT - Altera parametro de integra��o PSA; GET - Lista parametros de integra��o PSA; GET - Dashboard PEM logs de integracao; GET - Verifica se o email/cpf existe no cadastro de Participante; POST - Cadastra Participante Unifica��o de Acesso - TOTVSID; GET - Usu�rio PSA; POST - Usu�rio PSA; POST - Recurso Reservavel PSA; POST - Grava o json com propriedades de conex�o do PSA; GET - Retorna os motivos de NCC; POST - Cadastrar os motivos de NCC; GET - Retorna o log de faturamento do Zendesk com erro; GET - Produto grupo de servicos PSA; POST - Produto grupo de servicos PSA; GET - Segmento grupo de servicos PSA; POST - Segmento grupo de servicos PSA; GET - Condicao de Pagamento PSA; POST - Condicao de Pagamento PSA; GET - Tipo Entrada Saida TES; POST - Tipo Entrada Saida TES; GET - Item Contabil PSA; POST - Item Contabil PSA; GET - Classe Valor PSA; POST - Classe Valor PSA; GET - Centro de Custo PSA; POST - Centro de Custo PSA; GET - Lista os perfils do usuario PSA; PUT - Altera perfils do usuario PSA; GET - Propostas integracao PSA; GET - Retorna as moedas do Protheus para o PSA"
"TSRVX100.PRW","Servicos\Rotinas Genericas\TSRVX100.PRW","WsRestful auditoria  ""WS Auditoria Granting Access"" FORMAT 'application/json,text/html'","auditoria","GET - Retorna dados dos Participantes; GET - Retorna dados dos Fornecedores; GET - Retorna dados dos Usuarios ; POST - Retorna dados "
"TPACS001.PRW","Unificação de Acessos\WS\TPACS001.PRW","Fonte com os WebServices do Perfil de Acesso","Perfil_Acesso","LstCustos - Lista de centros de custo; LstPerfisCC - Traz uma lista dos perfis de acesso de um determinado CC; IntSolicit - Integracao com o cadastro de solicitacoes; IntRM - Cria AD de acordo com informa��es enviadas; LstZoom - Cria um zoom gen�rico de acordo com as informa��es passadas; LstGrupos - Lista grupos poss�veis para o perfil; Gestor - Retorna Gestor do Centro de Custo"
"TPACS002.PRW","Unificação de Acessos\WS\TPACS002.PRW","Fonte com as defini��es do WebServices para o processo Perfil de Acesso.","TOTVS_ID_UACESSO","UA_CONCEDE - Integra��o para participante(s); UA_PARTNER - Integra��o para parceiro(s); UA_DEPART - Integra��o para recis�o com participante(s) e parceiro(s); UA_LICENSE - Integra��o para apontamento de afastamento ou f�rias a participante(s)"
"WSCHANGEPSS.PRW","Unificação de Acessos\WS\WSCHANGEPSS.PRW","WsService CHANGEPSS  ""Servi�o para manuten��o do arquivo de Senhas do MAXIME"" NameSpace """"","CHANGEPSS","ChangeSysPsw - Metodo para alterar senha de usu�rio no MAXIME"
"ws_fluig.prw","Unificação de Acessos\WSClient\ws_fluig.prw","WebService - Descri��o n�o encontrada","",""
"WSCHAMTEC.prw","Unificação de Acessos\WSClient\WSCHAMTEC.prw","WebService - Descri��o n�o encontrada","",""
"WSPERFIL_ACESSO.prw","Unificação de Acessos\WSClient\WSPERFIL_ACESSO.prw","WebService - Descri��o n�o encontrada","",""
"WSPSScripts.prw","Unificação de Acessos\WSClient\WSPSScripts.prw","WebService - Descri��o n�o encontrada","",""
"WSPSSPSA.prw","Unificação de Acessos\WSClient\WSPSSPSA.prw","WebService - Descri��o n�o encontrada","",""
"TCTBS002.prw","Web Services\TCTBS002.prw","���           � WebService do protal de autoatendimento da Totvs.         ���","PLANO_CONTAS","PLANCTB - RETORNA RELA��O DE PLANO DE CONTAS; ITEMCTB - RETORNA RELA��O DE ITEM CONTABIL ; CLASSECTB - RETORNA RELA��O DE CLASSE DE VALOR "
"TCTBS003.prw","Web Services\TCTBS003.prw","WebService - Descri��o n�o encontrada","",""
"TCTBS005.PRW","Web Services\TCTBS005.PRW","Webservice da amarra��o das entidades contabeis.","AmarraEnt","GetDbkProt - Retorna amarra��o permitida "
"TSRVS001.prw","Web Services\TSRVS001.prw","Fonte onde esta configurado o webservice com os metodos do portal PMS","TSRVS001","AgAcessos - Retorna os acessos para a manutencao de agenda e O.S. para o usuario; BrwCustomer - Retorna lista de cliente; BrwPlace - Retorna lista dos locais de atendimento; BrwProjectCFP - Retorna lista dos projetos CFP; BrwReason - Retorna lista dos motivos; ConfirmSO - Confirmacao de ordem de servico; DelAgendaTecnico - Apaga agenda do tecnico; DelOrdemServico - Apaga ordem de servico; FechaRDA - Realiza o fechamento do RDA; GetDespesaRda - Retorna lista de despesas da Ordem de Servico (RDA); GetProjetoCFP - Retorna dados do projeto CFP (detalhes); ModuleList - Lista de modulos para o campo modulo da OS; PutAgendaTecnico - Grava agenda do tecnico; PutDespesaRda - Grava despesa da Ordem de Servico (RDA); RetAgendaPeriodo - Retorna dados da agenda do tecnico no periodo; TiposDespesa - Retorna lista com os tipos de despesas; RetProdFrente - Retorna lista de produtos para a frente de entrega; OSPrint - Retorna dados para impressao OS; ImprimeAgenda - Retorna dados para impressao do extrato da agenda do tencico; GetListaEDTPMS - Retorna lista de EDT; GetListaTarEdtPMS - Retorna lista de tarefas da EDT do projeto PMS; ImprimeNewRDA - Retorna dados para impressao do novo RDA; RetFrenteCFP - Retorna lista das frentes de entrega do projeto CFP; ImprimeRV - Retorna dados para impressao do relatorio de remuneracao variavel; RetornaTecnico - Retorna grupo/filial do tecnico pelo segmento, localidade e e-mail; RetDuracaoApto - Retorna a duracao do apontamento com base no calendario; RetKM - Retorna a quilometram para um cliente, loja, projeto, frente, data; RetCodKm - Retorna o codigo da despesa usada para quilometragem; GetListaProjetoCoorde - Retorna lista de projetos de um coordenador (frentes de entrega); BrwSegCli - Retorna lista dos segmentos do cliente; INCPVSUPTRF - Inclui pedido de venda a partir do chamado; INCPVTKZEN - Inclui pedido de venda a partir do ticket.; CONFRDA - Confer�ncia de RDA."
"TSRVS002.PRW","Web Services\TSRVS002.PRW","Fonte onde esta configurado o webservice com os metodo de Aprova��o","TSRVS002","Historico_OS - Lista Ordem de Servi�o; AprovRepr_OS - Aprova��o/Reprova��o de Ordem de Servi�o Eletronica"
"TSRVS003.prw","Web Services\TSRVS003.prw","Fonte onde esta configurado o webservice com os metodos do portal TPS","TSRVS003","GetComboAvaliacao - Retorna Lista de op�oes para o combo de notas (X3_CBOX); GetHistoricoAvaliacaoTecnico - Retorna lista do Historico de Avalia��o do Tecnico (Tabela PFS/PFT); GetListaAnalistaCoord - Retorna lista de analistas de um coordenador (Tabela RD0); GetListaAnalistaEmail - Retorna lista de analistas de um email (Tabela RD0); GetListaAnalistaProjeto - Retorna lista de Analistas que apontaram em um projeto (Tabela RD0); GetListaAvaliacaoGeral - Retorna lista de Avalia��es de um t�cnico em um per�odo(Tabela PFS/PFT); GetListaAvaliacaoPendente - Retorna lista de Avalia��es Pendentes; GetListaAvaliacaoTecnico - Retorna Avalia��o do Tecnico (Tabela PFS/PFT); GetListaClienteCoorde - Retorna lista de clientes dos projetos que o coordenador atuou (Tabela PE8); GetListaDinamicaGrd - Retorna Grade Dinamicamente; GetListaGrade - Retorna lista de grade por t�cnico (Tabela PFU); GetListaModulo - Retorna lista de modulos (ZX5 Tabela SRV033); GetListaConhecimento - Retorna lista de conhecimentos (Tabela PFQ); GetListaProduto - Retorna lista de produtos (ZX5 Tabela SRV031); GetListaProjetoCliente - Retorna lista de projetos do coordenador, filtrando o cliente ou nao (Tabela PE5); GetListaSegmento - Retorna lista de segmentos (ZX5 Tabela SRV030); GetNomeTecnico - Retorna Nome do T�cnico; PutAvaliacaoTecnico - Grava Avalia��o do T�cnico; GetListaUnidadeServico - Retorna lista de unidades de servico (Tabela PE1); GetListaGrupo - Retorna lista de grupos de conhecimento (ZX5 Tabela SRV032); GetListaDinamicaConhec - Retorna os conhecimentos - Faz busca dinamica e retorna os tecnicos e notas; GetCodigoFixo - Retorna codigos fixos de produto, segmento e modulo para avaliacao coordenador e projeto; GetListaAnalistaNome - Retorna Lista de Analistas pelo nome (RD0)"
"TSRVS004.PRW","Web Services\TSRVS004.PRW","Fonte onde esta configurado o webservice com os metodos do Milestone","TSRVS004","ListaProjeto - Retorna lista de projetos; ListaHistorico - Retorna lista de historicos para um projeto; ListaMilestone - Retorna lista de milestone para um historico; ListaPublicacao - Retorna lista de publicacoes para um projeto; BuscaDocumento - Retorna o caminho + documento para a publicacao; GravaCenario - Atualiza o cenario do projeto"
"TSRVS005.PRW","Web Services\TSRVS005.PRW","���     � Autentica usu�rio no Fluig.                           		   ���","AuthUsrFluig","GET - Autentica usu�rio no Fluig"
"WRTotvsDigital.PRW","Web Services\WRTotvsDigital.PRW","���     � WebREST Captura dados de tabelas Protheus.                 ���","RetLeeds","GET - Listagem dados de tabelas; POST - Inclui Prospect; POST - Transforma Prospect em Cliente; POST - Inclui Pedido; GET - Retorna Lista de Cart�o de Cr�dito; POST - Inclusao de Cart�o de Cr�dito; PUT - Altera��o de Cart�o de Cr�dito; DELETE - Exclus�o de Cart�o de Cr�dito; GET - Status Pedido Totvs Store; POST - Manuten��o Contatos; POST - Vincula��o de Contatos; GET - Consulta de Contatos; GET - Consulta de Unidades de Venda; POST - Cria atendimento; GET - Retorna Impostos; POST - Atualiza contatos; POST - Inclusao de contatos; POST - Altera dados de entidades; GET - Consulta pre�os de produtos; POST - Transforma Suspect em Cliente; GET - Informa��es de Cliente; GET - Informa��es de ESN, GSN, DSN e franquia; GET - Clientes sem visita do ESN, GSN ou DSN; GET - Oportunidades abertas do ESN, GSN ou DSN.; GET - T�tulos em aberto dos clientes do ESN, GSN ou DSN.; GET - Verifica��o de produtos do cliente; GET - T�tulos em aberto dos clientes do ESN, GSN ou DSN.; POST - Inclusao de cartoes de credito; GET - Oportunidades abertas dos ESNs de um GSN ou DSN."
"WRTUN001.prw","Web Services\WRTUN001.prw","WsRestFul WRTUN001  STR0001","",""
"WRTUN002.prw","Web Services\WRTUN002.prw","WsRestFul WRTUN002  STR0001","",""
"WRTUN003.prw","Web Services\WRTUN003.prw","WsRestFul WRTUN003  STR0001","",""
"WSChefes.prw","Web Services\WSChefes.prw","WebService - Descri��o n�o encontrada","",""
"WSECMTokenServiceService.prw","Web Services\WSECMTokenServiceService.prw","WebService - Descri��o n�o encontrada","",""
"WSFLUIGRM.prw","Web Services\WSFLUIGRM.prw","WebService - Descri��o n�o encontrada","",""
"WSINTPSA.prw","Web Services\WSINTPSA.prw","WsService WsPSAInteg  ""Servico Responsavel pela Integracao FLUIG X PSA X PROTHEUS""","WsPSAInteg","GetOfficials - Metodo Responsavel por Retornar os Funcionarios ( RD0 ) do Protheus; GetPSAPerfil - Metodo Responsavel por Retornar Perfil de Acesso PSA - DE/PARA de Centro de Custo X Cargo X Perfil no Protheus; PutAgePSA - Metodo Responsavel por Incluir Agendas no PSA; SetPSAAprov - Metodo Responsavel por Persistir os Dados de Aprovacao de Usuarios PSA via FLUIG; GetStatusAuditoria - Metodo responsavel por validar o status do usuario nas aplicacoes AD TOTVS/ AD AZURE/ PSA / PROTHEUS..."
"WSO2glbssl.prw","Web Services\WSO2glbssl.prw","WebService - Descri��o n�o encontrada","",""
"WSwsGlbSSL.prw","Web Services\WSwsGlbSSL.prw","WebService - Descri��o n�o encontrada","",""
